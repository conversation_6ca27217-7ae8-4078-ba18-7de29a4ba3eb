/**
 * MongoDB Setup Script for Universal Odoo Adapter
 * 
 * This script sets up the MongoDB database with proper indexes and collections
 * Run with: node scripts/setup-mongodb.js
 */

const { MongoClient } = require('mongodb');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://127.0.0.1:27018/universal-odoo-adapter';
const DATABASE_NAME = 'universal-odoo-adapter';

async function setupMongoDB() {
  let client;
  
  try {
    console.log('🔗 Connecting to MongoDB...');
    client = new MongoClient(MONGODB_URI);
    await client.connect();
    
    const db = client.db(DATABASE_NAME);
    console.log(`✅ Connected to database: ${DATABASE_NAME}`);

    // Setup user_odoo_mappings collection
    console.log('📋 Setting up user_odoo_mappings collection...');
    const mappingsCollection = db.collection('user_odoo_mappings');

    // Create indexes
    console.log('🔍 Creating indexes...');
    
    // Compound unique index for user + instance
    await mappingsCollection.createIndex(
      { webUserId: 1, odooInstanceId: 1 }, 
      { unique: true, name: 'user_instance_unique' }
    );
    
    // Index for active mappings by user
    await mappingsCollection.createIndex(
      { webUserId: 1, isActive: 1 }, 
      { name: 'user_active_mappings' }
    );
    
    // Index for last used sorting
    await mappingsCollection.createIndex(
      { webUserId: 1, lastUsed: -1 }, 
      { name: 'user_last_used' }
    );
    
    // Index for Odoo instance lookup
    await mappingsCollection.createIndex(
      { odooHost: 1, odooDatabase: 1, odooUsername: 1 }, 
      { name: 'odoo_instance_lookup' }
    );
    
    // Index for active mappings
    await mappingsCollection.createIndex(
      { isActive: 1 }, 
      { name: 'active_mappings' }
    );
    
    // Index for timestamps
    await mappingsCollection.createIndex(
      { createdAt: 1 }, 
      { name: 'created_at' }
    );
    
    await mappingsCollection.createIndex(
      { updatedAt: 1 }, 
      { name: 'updated_at' }
    );

    // Create sample data for development
    if (process.env.NODE_ENV === 'development') {
      console.log('🧪 Creating sample data for development...');
      
      const sampleMapping = {
        webUserId: 'dev_user_001',
        odooInstanceId: 'odoo_sample_001',
        odooHost: 'https://demo.odoo.com',
        odooDatabase: 'demo',
        odooUsername: 'demo',
        odooPasswordEncrypted: Buffer.from('demo').toString('base64'), // Simple encoding for demo
        odooProtocol: 'https',
        odooPort: 443,
        isActive: true,
        lastUsed: new Date(),
        description: 'Sample Odoo Demo Instance',
        tags: ['demo', 'development'],
        totalUsageCount: 0,
        connectionAttempts: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      try {
        await mappingsCollection.insertOne(sampleMapping);
        console.log('✅ Sample data created');
      } catch (error) {
        if (error.code === 11000) {
          console.log('ℹ️  Sample data already exists');
        } else {
          throw error;
        }
      }
    }

    // Verify setup
    console.log('🔍 Verifying setup...');
    const indexes = await mappingsCollection.indexes();
    console.log(`✅ Created ${indexes.length} indexes:`);
    indexes.forEach(index => {
      console.log(`   - ${index.name}: ${JSON.stringify(index.key)}`);
    });

    const count = await mappingsCollection.countDocuments();
    console.log(`📊 Collection contains ${count} documents`);

    console.log('🎉 MongoDB setup completed successfully!');
    
  } catch (error) {
    console.error('❌ MongoDB setup failed:', error);
    process.exit(1);
  } finally {
    if (client) {
      await client.close();
      console.log('🔌 Disconnected from MongoDB');
    }
  }
}

// Run setup if called directly
if (require.main === module) {
  setupMongoDB();
}

module.exports = { setupMongoDB };

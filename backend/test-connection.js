const mongoose = require('mongoose');

console.log('Testing MongoDB connection...');
console.log('Node.js version:', process.version);
console.log('Mongoose version:', mongoose.version);

async function testConnection() {
  try {
    console.log('\n🔍 Testing connection to 127.0.0.1:27018...');

    const connection = await mongoose.createConnection('mongodb://127.0.0.1:27018/test', {
      family: 4,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 5000,
    });

    console.log('✅ Successfully connected to MongoDB on port 27018');
    await connection.close();
    process.exit(0);

  } catch (err) {
    console.error('❌ Failed to connect to port 27018:', err.message);
    console.error('Full error:', err);
    process.exit(1);
  }
}

testConnection();

# Universal Odoo Adapter Configuration

# Server Configuration
NODE_ENV=development
PORT=3000

# MongoDB Configuration - Use 127.0.0.1 instead of localhost for Node.js v17+ compatibility
MONGODB_URI=mongodb://127.0.0.1:27018/universal-odoo-adapter
# For MongoDB Atlas:
# MONGODB_URI=mongodb+srv://username:<EMAIL>/universal-odoo-adapter?retryWrites=true&w=majority

# Security Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRATION=24h
ENCRYPTION_KEY=your-32-character-encryption-key-here

# Redis Configuration (Optional - for advanced caching)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Logging Configuration
LOG_LEVEL=debug
LOG_FORMAT=json

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
CORS_METHODS=GET,HEAD,PUT,PATCH,POST,DELETE
CORS_CREDENTIALS=true

# Rate Limiting (optional)
THROTTLE_TTL=60
THROTTLE_LIMIT=100

# Connection Pool Configuration
MAX_POOL_SIZE=10
CONNECTION_TIMEOUT=30000
IDLE_TIMEOUT=300000

# Health Check Configuration
HEALTH_CHECK_INTERVAL=300000
CONNECTION_RETRY_ATTEMPTS=3

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_LOG_THRESHOLD=1000

# Development Settings
ENABLE_SWAGGER=true
ENABLE_CORS=true

# Production Settings (uncomment for production)
# NODE_ENV=production
# LOG_LEVEL=info
# ENABLE_SWAGGER=false

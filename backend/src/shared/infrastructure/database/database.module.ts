import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { UserOdooMapping, UserOdooMappingSchema } from './schemas/user-odoo-mapping.schema';

console.log('🚀 [DatabaseModule] Loading DatabaseModule...');

@Module({
  imports: [
    // MongoDB connection - use default connection with explicit configuration
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const uri = configService.get<string>('MONGODB_URI') ||
                   'mongodb://127.0.0.1:27018/universal-odoo-adapter';

        console.log('🔗 [DatabaseModule] MONGODB_URI from env:', configService.get<string>('MONGODB_URI'));
        console.log('🔗 [DatabaseModule] Final URI:', uri);
        console.log('🔗 [DatabaseModule] NODE_ENV:', process.env.NODE_ENV);

        return {
          uri,
          // Force IPv4 to avoid Node.js v17+ IPv6 resolution issues
          family: 4,
          // Connection pool settings
          maxPoolSize: 10,
          serverSelectionTimeoutMS: 5000,
          socketTimeoutMS: 45000,
          // Retry settings
          retryWrites: true,
          retryReads: true,
          // Compression
          compressors: ['zlib'],
          // Monitoring
          monitorCommands: process.env.NODE_ENV === 'development',
        };
      },
      inject: [ConfigService],
    }),

    // Register schemas for the default connection
    MongooseModule.forFeature([
      { name: UserOdooMapping.name, schema: UserOdooMappingSchema },
    ]),
  ],
  exports: [
    MongooseModule,
  ],
})
export class DatabaseModule {}

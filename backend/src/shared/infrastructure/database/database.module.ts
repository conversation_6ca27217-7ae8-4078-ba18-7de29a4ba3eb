import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { UserOdooMapping, UserOdooMappingSchema } from './schemas/user-odoo-mapping.schema';

console.log('🚀 [DatabaseModule] Loading DatabaseModule...');

@Module({
  imports: [
    // MongoDB connection with simple configuration - test direct approach
    MongooseModule.forRoot('mongodb://localhost:27018/universal-odoo-adapter', {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      retryWrites: true,
      retryReads: true,
      compressors: ['zlib'],
    }),

    // Register schemas for the default connection
    MongooseModule.forFeature([
      { name: UserOdooMapping.name, schema: UserOdooMappingSchema },
    ]),
  ],
  exports: [
    MongooseModule,
  ],
})
export class DatabaseModule {}

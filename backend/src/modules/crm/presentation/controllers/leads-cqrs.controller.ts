import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpCode,
  UseGuards,
  Logger,
  Version,
} from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { ApiTags, ApiOperation, ApiR<PERSON>ponse, Api<PERSON>earerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '@/shared/infrastructure/guards/jwt-auth.guard';
import { CurrentUser } from '@/shared/infrastructure/decorators/current-user.decorator';
import { CreateLeadCommand } from '@/modules/crm/application/cqrs/commands/create-lead.command';
import { ConvertLeadToOpportunityCommand } from '@/modules/crm/application/cqrs/commands/convert-lead-to-opportunity.command';
import { GetPipelineAnalyticsQuery } from '@/modules/crm/application/cqrs/queries/get-pipeline-analytics.query';
import { LeadPriority } from '@/modules/crm/domain/value-objects/lead-priority.vo';
import { LeadType } from '@/modules/crm/domain/value-objects/lead-type.vo';

// DTOs and Response Types
type CreateLeadCqrsDto = {
  name: string;
  email?: string;
  phone?: string;
  company?: string;
  website?: string;
  address?: string;
  city?: string;
  country?: string;
  description?: string;
  priority?: number;
  type?: string;
  source?: string;
  expectedRevenue?: number;
  probability?: number;
  assignedUserId?: number;
  teamId?: number;
  campaignId?: number;
  sourceId?: number;
  mediumId?: number;
  tags?: string[];
  dateDeadline?: string;
}

type ConvertLeadCqrsDto = {
  opportunityName?: string;
  expectedRevenue: number;
  probability?: number;
  closeDate?: string;
  partnerId?: number;
  stageId?: number;
  assignedUserId?: number;
  dateDeadline?: string;
  description?: string;
  reason?: string;
}

type CreateLeadCqrsResponse = {
  success: boolean;
  message: string;
  data: {
    leadId: string;
    lead: any;
  };
  metadata: {
    commandType: string;
    executedAt: string;
    executedBy: string;
  };
}

type ConvertLeadCqrsResponse = {
  success: boolean;
  message: string;
  data: {
    leadId: number;
    opportunityId: string;
    opportunity: any;
  };
  metadata: {
    commandType: string;
    executedAt: string;
    executedBy: string;
  };
}

type PipelineAnalyticsCqrsResponse = {
  success: boolean;
  message: string;
  data: any;
  metadata: {
    queryType: string;
    executedAt: string;
    executedBy: string;
    cacheKey?: string;
    [key: string]: any;
  };
}

type PipelineAnalyticsCqrsQueryDto = {
  teamId?: number;
  userId?: number;
  dateFrom?: string;
  dateTo?: string;
  includeStageMetrics?: boolean;
  includeConversionRates?: boolean;
  includeBottlenecks?: boolean;
  includeForecast?: boolean;
  includeComparisons?: boolean;
  stageIds?: number[];
  priorityLevels?: string[];
  sources?: string[];
  groupBy?: 'stage' | 'team' | 'user' | 'source' | 'priority';
  timeGranularity?: 'day' | 'week' | 'month' | 'quarter';
}

/**
 * CQRS-based Leads Controller
 * Demonstrates the new CQRS pattern implementation
 */
@ApiTags('CRM - Leads (CQRS)')
@Controller({ path: 'crm/leads-cqrs', version: '1' })
// @UseGuards(JwtAuthGuard) // Temporarily disabled for testing
// @ApiBearerAuth()
export class LeadsCqrsController {
  private readonly logger = new Logger(LeadsCqrsController.name);

  constructor(
    private readonly commandBus: CommandBus,
    private readonly queryBus: QueryBus,
  ) {}

  /**
   * Test endpoint to verify controller is working
   */
  @Get('test')
  @HttpCode(HttpStatus.OK)
  test() {
    return {
      success: true,
      message: 'LeadsCqrsController is working!',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Create a new lead using CQRS command
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new lead (CQRS)' })
  @ApiResponse({ status: 201, description: 'Lead created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async createLead(
    @Body() createLeadDto: CreateLeadCqrsDto,
    @CurrentUser() user: any,
  ): Promise<CreateLeadCqrsResponse> {
    this.logger.log(`Creating lead: ${createLeadDto.name} by user: ${user.id}`);

    try {
      // Map DTO to command
      const command = new CreateLeadCommand(
        createLeadDto.name,
        createLeadDto.email,
        createLeadDto.phone,
        createLeadDto.company,
        createLeadDto.website,
        createLeadDto.address,
        createLeadDto.city,
        createLeadDto.country,
        createLeadDto.source || 'website',
        createLeadDto.type ? LeadType.fromValue(createLeadDto.type) : LeadType.LEAD,
        createLeadDto.priority ? LeadPriority.fromValue(createLeadDto.priority) : LeadPriority.MEDIUM,
        createLeadDto.expectedRevenue,
        createLeadDto.probability,
        createLeadDto.description,
        createLeadDto.assignedUserId,
        createLeadDto.teamId,
        createLeadDto.campaignId,
        createLeadDto.sourceId,
        createLeadDto.mediumId,
        createLeadDto.tags || [],
        createLeadDto.dateDeadline ? new Date(createLeadDto.dateDeadline) : undefined,
        user.id, // createdBy
        user.companyId,
      );

      // Execute command
      const result = await this.commandBus.execute(command);

      return {
        success: true,
        message: 'Lead created successfully',
        data: {
          leadId: result.leadId,
          lead: result.lead.toPlainObject(),
        },
        metadata: {
          commandType: 'CreateLeadCommand',
          executedAt: new Date().toISOString(),
          executedBy: user.id,
        },
      };

    } catch (error) {
      this.logger.error(`Failed to create lead: ${createLeadDto.name}`, error);
      throw error;
    }
  }

  /**
   * Convert lead to opportunity using CQRS command
   */
  @Post(':id/convert-to-opportunity')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Convert lead to opportunity (CQRS)' })
  @ApiResponse({ status: 200, description: 'Lead converted successfully' })
  @ApiResponse({ status:404, description: 'Lead not found' })
  @ApiResponse({ status: 400, description: 'Lead cannot be converted' })
  async convertToOpportunity(
    @Param('id') leadId: number,
    @Body() convertDto: ConvertLeadCqrsDto,
    @CurrentUser() user: any,
  ): Promise<ConvertLeadCqrsResponse> {
    this.logger.log(`Converting lead ${leadId} to opportunity by user: ${user.id}`);

    try {
      // Create command
      const command = new ConvertLeadToOpportunityCommand(
        leadId,
        convertDto.expectedRevenue,
        convertDto.probability || 10,
        convertDto.partnerId,
        convertDto.stageId,
        convertDto.assignedUserId,
        convertDto.dateDeadline ? new Date(convertDto.dateDeadline) : undefined,
        convertDto.description,
        user.id, // convertedBy
        convertDto.reason,
      );

      // Execute command
      const result = await this.commandBus.execute(command);

      return {
        success: true,
        message: 'Lead converted to opportunity successfully',
        data: {
          leadId,
          opportunityId: result.opportunityId,
          opportunity: result.opportunity.toPlainObject(),
        },
        metadata: {
          commandType: 'ConvertLeadToOpportunityCommand',
          executedAt: new Date().toISOString(),
          executedBy: user.id,
        },
      };

    } catch (error) {
      this.logger.error(`Failed to convert lead ${leadId} to opportunity`, error);
      throw error;
    }
  }

  /**
   * Get pipeline analytics using CQRS query
   */
  @Get('analytics/pipeline')
  @ApiOperation({ summary: 'Get pipeline analytics (CQRS)' })
  @ApiResponse({ status: 200, description: 'Pipeline analytics retrieved successfully' })
  async getPipelineAnalytics(
    @Query() analyticsQuery: PipelineAnalyticsCqrsQueryDto,
    @CurrentUser() user: any,
  ): Promise<PipelineAnalyticsCqrsResponse> {
    this.logger.log(`Getting pipeline analytics for user: ${user.id}`);

    try {
      // Create query
      const query = new GetPipelineAnalyticsQuery(
        analyticsQuery.teamId,
        analyticsQuery.userId,
        analyticsQuery.dateFrom ? new Date(analyticsQuery.dateFrom) : undefined,
        analyticsQuery.dateTo ? new Date(analyticsQuery.dateTo) : undefined,
        analyticsQuery.includeStageMetrics !== false,
        analyticsQuery.includeConversionRates !== false,
        analyticsQuery.includeBottlenecks !== false,
        analyticsQuery.includeForecast !== false,
        analyticsQuery.includeComparisons || false,
        analyticsQuery.stageIds,
        analyticsQuery.priorityLevels,
        analyticsQuery.sources,
        analyticsQuery.groupBy,
        analyticsQuery.timeGranularity,
        user.id, // requestedBy
      );

      // Execute query
      const result = await this.queryBus.execute(query);

      return {
        success: true,
        message: 'Pipeline analytics retrieved successfully',
        data: result,
        metadata: {
          queryType: 'GetPipelineAnalyticsQuery',
          executedAt: new Date().toISOString(),
          executedBy: user.id,
          cacheKey: query.getCacheKey(),
        },
      };

    } catch (error) {
      this.logger.error('Failed to get pipeline analytics', error);
      throw error;
    }
  }
}

// DTOs for request/response
interface CreateLeadDto {
  name: string;
  email?: string;
  phone?: string;
  company?: string;
  website?: string;
  address?: string;
  city?: string;
  country?: string;
  source?: string;
  type?: string;
  priority?: number;
  expectedRevenue?: number;
  probability?: number;
  description?: string;
  assignedUserId?: number;
  teamId?: number;
  campaignId?: number;
  sourceId?: number;
  mediumId?: number;
  tags?: string[];
  dateDeadline?: string;
}

interface ConvertLeadDto {
  expectedRevenue: number;
  probability?: number;
  partnerId?: number;
  stageId?: number;
  assignedUserId?: number;
  dateDeadline?: string;
  description?: string;
  reason?: string;
}

interface PipelineAnalyticsQueryDto {
  teamId?: number;
  userId?: number;
  dateFrom?: string;
  dateTo?: string;
  includeStageMetrics?: boolean;
  includeConversionRates?: boolean;
  includeBottlenecks?: boolean;
  includeForecast?: boolean;
  includeComparisons?: boolean;
  stageIds?: number[];
  priorityLevels?: string[];
  sources?: string[];
  groupBy?: 'stage' | 'team' | 'user' | 'source' | 'priority';
  timeGranularity?: 'day' | 'week' | 'month' | 'quarter';
}

interface CreateLeadResponse {
  success: boolean;
  message: string;
  data: {
    leadId: number;
    lead: any;
  };
  metadata: any;
}

interface ConvertLeadResponse {
  success: boolean;
  message: string;
  data: {
    leadId: number;
    opportunityId: number;
    opportunity: any;
  };
  metadata: any;
}

interface PipelineAnalyticsResponse {
  success: boolean;
  message: string;
  data: any;
  metadata: any;
}

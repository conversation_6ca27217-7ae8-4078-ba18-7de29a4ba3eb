import { Modu<PERSON> } from '@nestjs/common';
import { CqrsModule as NestCqrsModule } from '@nestjs/cqrs';
import { EventEmitterModule } from '@nestjs/event-emitter';

// Command Handlers (Traditional)
import { CreateLeadHandler } from './commands/handlers/create-lead.handler';
import { UpdateLeadHandler } from './commands/handlers/update-lead.handler';
import { ConvertLeadToOpportunityHandler } from './commands/handlers/convert-lead-to-opportunity.handler';
import { UpdateLeadPriorityHandler } from './commands/handlers/update-lead-priority.handler';
import { DeleteLeadHandler } from './commands/handlers/delete-lead.handler';
import { AssignLeadToUserHandler } from './commands/handlers/assign-lead-to-user.handler';
import { AssignLeadToTeamHandler } from './commands/handlers/assign-lead-to-team.handler';
import { UpdateRevenueForecastHandler } from './commands/handlers/update-revenue-forecast.handler';
import { SetLeadDeadlineHandler } from './commands/handlers/set-lead-deadline.handler';
import { AddLeadTagHandler } from './commands/handlers/add-lead-tag.handler';
import { RemoveLeadTagHandler } from './commands/handlers/remove-lead-tag.handler';

// Event Sourcing Command Handlers - temporarily disabled to test connection
// import { CreateLeadESHandler } from './commands/handlers/create-lead-es.handler';
// import { UpdateLeadESHandler } from './commands/handlers/update-lead-es.handler';
// import { AssignLeadESHandler } from './commands/handlers/assign-lead-es.handler';
// import { ChangeLeadStatusESHandler } from './commands/handlers/change-lead-status-es.handler';

// Repository Dependencies
import { OdooLeadRepository } from '@/modules/crm/infrastructure/repositories/odoo-lead.repository';
import { OdooStageRepository } from '@/modules/crm/infrastructure/repositories/odoo-stage.repository';
import { OdooTeamRepository } from '@/modules/crm/infrastructure/repositories/odoo-team.repository';
import { OdooUserRepository } from '@/modules/crm/infrastructure/repositories/odoo-user.repository';
import { LEAD_REPOSITORY_TOKEN, STAGE_REPOSITORY_TOKEN, TEAM_REPOSITORY_TOKEN, USER_REPOSITORY_TOKEN } from '@/modules/crm/domain/repositories/injection-tokens';

// Event Sourcing Module - temporarily disabled to test connection
// import { EventSourcingModule } from '@/modules/crm/infrastructure/event-sourcing/event-sourcing.module';

// Cache Module
import { CacheModule } from '@/shared/infrastructure/cache';

// Query Handlers
import { GetLeadByIdHandler } from './queries/handlers/get-lead-by-id.handler';
import { GetLeadsByFiltersHandler } from './queries/handlers/get-leads-by-filters.handler';
import { GetPipelineAnalyticsHandler } from './queries/handlers/get-pipeline-analytics.handler';
import { GetLeadStatisticsHandler } from './queries/handlers/get-lead-statistics.handler';
import { GetOverdueLeadsHandler } from './queries/handlers/get-overdue-leads.handler';
import { GetLeadsRequiringAttentionHandler } from './queries/handlers/get-leads-requiring-attention.handler';
import { SearchLeadsHandler } from './queries/handlers/search-leads.handler';

// Event Handlers
import { LeadCreatedHandler } from './events/handlers/lead-created.handler';
import { LeadStatusChangedHandler } from './events/handlers/lead-status-changed.handler';
import { LeadConvertedToOpportunityHandler } from './events/handlers/lead-converted-to-opportunity.handler';
import { LeadAssignedHandler } from './events/handlers/lead-assigned.handler';
import { LeadPriorityChangedHandler } from './events/handlers/lead-priority-changed.handler';

// Sagas
import { LeadManagementSaga } from './sagas/lead-management.saga';

/**
 * CQRS Module for CRM
 * Configures command handlers, query handlers, event handlers, and sagas
 */
@Module({
  imports: [
    NestCqrsModule,
    // EventSourcingModule, // Temporarily disabled to test connection
    CacheModule,
    EventEmitterModule.forRoot({
      // Use this instance across the whole app
      global: true,
      // Set this to `true` to use wildcards
      wildcard: false,
      // The delimiter used to segment namespaces
      delimiter: '.',
      // Set this to `true` if you want to emit the newListener event
      newListener: false,
      // Set this to `true` if you want to emit the removeListener event
      removeListener: false,
      // The maximum amount of listeners that can be assigned to an event
      maxListeners: 10,
      // Show event name in memory leak message when more than maximum amount of listeners is assigned
      verboseMemoryLeak: false,
      // Disable throwing uncaughtException if an error event is emitted and it has no listeners
      ignoreErrors: false,
    }),
  ],
  providers: [
    // Traditional Command Handlers
    CreateLeadHandler,
    UpdateLeadHandler,
    DeleteLeadHandler,
    ConvertLeadToOpportunityHandler,
    UpdateLeadPriorityHandler,
    AssignLeadToUserHandler,
    AssignLeadToTeamHandler,
    UpdateRevenueForecastHandler,
    SetLeadDeadlineHandler,
    AddLeadTagHandler,
    RemoveLeadTagHandler,

    // Event Sourcing Command Handlers - temporarily disabled to test connection
    // CreateLeadESHandler,
    // UpdateLeadESHandler,
    // AssignLeadESHandler,
    // ChangeLeadStatusESHandler,

    // Repository implementations
    {
      provide: LEAD_REPOSITORY_TOKEN,
      useClass: OdooLeadRepository,
    },
    {
      provide: STAGE_REPOSITORY_TOKEN,
      useClass: OdooStageRepository,
    },
    {
      provide: TEAM_REPOSITORY_TOKEN,
      useClass: OdooTeamRepository,
    },
    {
      provide: USER_REPOSITORY_TOKEN,
      useClass: OdooUserRepository,
    },

    // Query Handlers
    GetLeadByIdHandler,
    GetLeadsByFiltersHandler,

    GetPipelineAnalyticsHandler,
    GetLeadStatisticsHandler,
    GetOverdueLeadsHandler,
    GetLeadsRequiringAttentionHandler,
    SearchLeadsHandler,

    // Event Handlers
    LeadCreatedHandler,
    LeadStatusChangedHandler,
    LeadConvertedToOpportunityHandler,
    LeadAssignedHandler,
    LeadPriorityChangedHandler,

    // Sagas
    LeadManagementSaga,
  ],
  exports: [
    NestCqrsModule,
    EventEmitterModule,
  ],
})
export class CrmCqrsModule {}
